#!/usr/bin/env bash
set -e

############ 1. 环境变量检查 ############
echo "检查必要环境变量..."

# CockroachDB 配置检查
if [ -z "$DB_HOST" ] || [ -z "$DB_DATABASE" ] || [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ]; then
    echo "错误: 缺少 CockroachDB 配置环境变量"
    echo "必需变量: DB_HOST, DB_DATABASE, DB_USERNAME, DB_PASSWORD"
    exit 1
fi

# Redis 配置检查
if [ -z "$REDIS_HOST" ]; then
    echo "错误: 缺少 REDIS_HOST 环境变量"
    exit 1
fi

############ 2. CockroachDB 证书处理 ############
echo "配置 CockroachDB SSL 证书..."

# 使用一个 Spaces 可写目录来存放证书
echo "HOME: $HOME"
echo "PWD: $(pwd)"
echo "USER: $USER"
CERTS_DIR=${CERTS_DIR:-/tmp/certs}
mkdir -p "$CERTS_DIR"
chmod 755 "$CERTS_DIR" 2>/dev/null || true

# 检查是否提供了证书下载 URL
if [ -n "$COCKROACH_CERT_URL" ]; then
    echo "从 CockroachDB Cloud 下载证书: $COCKROACH_CERT_URL"
    if curl -s -f -o /tmp/root.crt "$COCKROACH_CERT_URL"; then
        mv /tmp/root.crt "$CERTS_DIR/root.crt"
        chmod 644 "$CERTS_DIR/root.crt" 2>/dev/null || true
        echo "证书下载成功"
        export DB_SSL_CA_CERT="$CERTS_DIR/root.crt"
    else
        echo "警告: 证书下载失败，将尝试不验证证书的连接"
        export DB_SSL_MODE="require"
    fi
elif [ -n "$COCKROACH_CA_CERT" ]; then
    echo "使用环境变量中的 CA 证书..."
    echo "$COCKROACH_CA_CERT" > "$CERTS_DIR/root.crt"
    chmod 644 "$CERTS_DIR/root.crt" 2>/dev/null || true
    export DB_SSL_CA_CERT="$CERTS_DIR/root.crt"
else
    echo "未提供证书信息，使用默认 SSL 模式..."
    export DB_SSL_MODE="require"
fi

# 检查是否提供了客户端证书和密钥（通常 CockroachDB Cloud 不需要）
if [ -n "$COCKROACH_CLIENT_CERT" ] && [ -n "$COCKROACH_CLIENT_KEY" ]; then
    echo "配置客户端证书认证..."
    echo "$COCKROACH_CLIENT_CERT" > /app/certs/client.crt
    echo "$COCKROACH_CLIENT_KEY" > /app/certs/client.key
    chmod 644 /app/certs/client.crt
    chmod 600 /app/certs/client.key
    export DB_SSL_CLIENT_CERT="/app/certs/client.crt"
    export DB_SSL_CLIENT_KEY="/app/certs/client.key"
fi

############ 3. 数据库配置 ##########
export DATABASE_TYPE=postgresql
export DB_USERNAME=${DB_USERNAME:-}
export DB_PASSWORD=${DB_PASSWORD:-}
export DB_HOST=${DB_HOST:-}
export DB_PORT=${DB_PORT:-26257}
export DB_DATABASE=${DB_DATABASE:-}

# 设置数据库连接池配置
export SQLALCHEMY_POOL_SIZE=${SQLALCHEMY_POOL_SIZE:-30}
export SQLALCHEMY_POOL_RECYCLE=${SQLALCHEMY_POOL_RECYCLE:-3600}
export SQLALCHEMY_ECHO=${SQLALCHEMY_ECHO:-false}

# CockroachDB SSL 模式配置
export DB_SSL_MODE=${DB_SSL_MODE:-require}

# 构建 CockroachDB 连接字符串
CONNECTION_PARAMS="sslmode=${DB_SSL_MODE}"

# 添加 CA 证书参数
if [ -n "$DB_SSL_CA_CERT" ]; then
    CONNECTION_PARAMS="${CONNECTION_PARAMS}&sslrootcert=${DB_SSL_CA_CERT}"
fi

# 添加客户端证书参数
if [ -n "$DB_SSL_CLIENT_CERT" ] && [ -n "$DB_SSL_CLIENT_KEY" ]; then
    CONNECTION_PARAMS="${CONNECTION_PARAMS}&sslcert=${DB_SSL_CLIENT_CERT}&sslkey=${DB_SSL_CLIENT_KEY}"
fi

# 添加 CockroachDB 特定参数
CONNECTION_PARAMS="${CONNECTION_PARAMS}&application_name=dify"

export SQLALCHEMY_DATABASE_URI="postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}?${CONNECTION_PARAMS}"
echo "SQLALCHEMY_DATABASE_URI: $SQLALCHEMY_DATABASE_URI"
export DB_EXTRAS="sslmode=verify-full&sslrootcert=/tmp/certs/root.crt&application_name=dify"
export SQLALCHEMY_DATABASE_URI_SCHEME="cockroachdb"
echo "CockroachDB 连接: ${DB_HOST}:${DB_PORT}/${DB_DATABASE} (SSL: ${DB_SSL_MODE})"

############ 3. Redis 配置 ##########
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_DB=${REDIS_DB:-0}
export REDIS_USERNAME=${REDIS_USERNAME:-}
export REDIS_PASSWORD=${REDIS_PASSWORD:-}
export REDIS_USE_SSL=${REDIS_USE_SSL:-false}

# 支持 Redis Sentinel 配置
export REDIS_USE_SENTINEL=${REDIS_USE_SENTINEL:-false}
export REDIS_SENTINELS=${REDIS_SENTINELS:-}
export REDIS_SENTINEL_SERVICE_NAME=${REDIS_SENTINEL_SERVICE_NAME:-}
export REDIS_SENTINEL_USERNAME=${REDIS_SENTINEL_USERNAME:-}
export REDIS_SENTINEL_PASSWORD=${REDIS_SENTINEL_PASSWORD:-}
export REDIS_SENTINEL_SOCKET_TIMEOUT=${REDIS_SENTINEL_SOCKET_TIMEOUT:-0.1}

echo "Redis 连接: ${REDIS_HOST}:${REDIS_PORT} (DB: ${REDIS_DB})"

############ 4. 连接测试 ##########
echo "测试 CockroachDB 连接..."
cd /app/api

# 使用 Python 测试 CockroachDB 连接
python3 -c "
import psycopg2
import sys
import os

try:
    # 构建连接参数
    conn_params = {
        'host': '${DB_HOST}',
        'port': ${DB_PORT},
        'database': '${DB_DATABASE}',
        'user': '${DB_USERNAME}',
        'password': '${DB_PASSWORD}',
        'sslmode': '${DB_SSL_MODE}'
    }

    # 添加 SSL 证书参数
    if os.getenv('DB_SSL_CA_CERT'):
        conn_params['sslrootcert'] = os.getenv('DB_SSL_CA_CERT')
    if os.getenv('DB_SSL_CLIENT_CERT'):
        conn_params['sslcert'] = os.getenv('DB_SSL_CLIENT_CERT')
    if os.getenv('DB_SSL_CLIENT_KEY'):
        conn_params['sslkey'] = os.getenv('DB_SSL_CLIENT_KEY')

    conn = psycopg2.connect(**conn_params)

    # 测试 CockroachDB 版本
    with conn.cursor() as cur:
        cur.execute('SELECT version()')
        version = cur.fetchone()[0]
        print(f'CockroachDB 连接成功: {version.split()[0]} {version.split()[1]}')

    conn.close()
except Exception as e:
    print(f'CockroachDB 连接失败: {e}')
    sys.exit(1)
"

# 使用 Python 测试 Redis 连接
python3 -c "
import redis
import sys
try:
    # 根据配置构建 Redis 连接
    redis_kwargs = {
        'host': '${REDIS_HOST}',
        'port': ${REDIS_PORT},
        'db': ${REDIS_DB},
        'ssl': ${REDIS_USE_SSL}
    }

    if '${REDIS_USERNAME}':
        redis_kwargs['username'] = '${REDIS_USERNAME}'
    if '${REDIS_PASSWORD}':
        redis_kwargs['password'] = '${REDIS_PASSWORD}'

    r = redis.Redis(**redis_kwargs)
    r.ping()
    print('Redis 连接成功')
except Exception as e:
    print(f'Redis 连接失败: {e}')
    sys.exit(1)
"

############ 5. 数据库迁移 #############
echo "执行数据库迁移..."

# 容错执行迁移
migrate_database() {
    echo "尝试执行数据库迁移..."

    # 尝试执行迁移，捕获输出和错误码
    if MIGRATION_OUTPUT=$(FLASK_APP=app.py flask db upgrade 2>&1); then
        echo "数据库迁移成功完成"
        echo "$MIGRATION_OUTPUT"
        return 0
    else
        echo "迁移遇到错误，分析原因..."
        echo "错误信息: $MIGRATION_OUTPUT"

        # 检查各种可跳过的错误类型
        if echo "$MIGRATION_OUTPUT" | grep -qi "already exists\|duplicate\|重复\|DuplicateColumn\|UndefinedTable\|does not exist"; then
            echo "检测到可跳过的数据库错误，尝试修复..."

            # 从错误信息中提取目标迁移版本
            TARGET_REVISION=$(echo "$MIGRATION_OUTPUT" | grep -oP "upgrade.*?-> \K[a-f0-9]+" | head -1)

            if [ ! -z "$TARGET_REVISION" ]; then
                echo "检测到目标迁移版本: $TARGET_REVISION"

                # 判断错误类型
                if echo "$MIGRATION_OUTPUT" | grep -qi "already exists\|duplicate\|DuplicateColumn"; then
                    echo "字段/表已存在，跳过此迁移..."
                elif echo "$MIGRATION_OUTPUT" | grep -qi "UndefinedTable\|does not exist"; then
                    echo "表不存在（可能已被删除或重构），跳过此迁移..."
                fi

                echo "标记迁移 $TARGET_REVISION 为已完成..."
                FLASK_APP=app.py flask db stamp $TARGET_REVISION

                echo "继续执行后续迁移..."
                # 递归重试，处理可能的多个问题
                migrate_database
                return $?
            else
                echo "无法从错误信息中提取迁移版本，尝试其他方法..."
                handle_migration_fallback
                return 0
            fi
        else
            echo "遇到无法自动处理的迁移错误："
            echo "$MIGRATION_OUTPUT"
            echo "迁移失败但继续启动服务..."
            return 0
        fi
    fi
}

# 备用处理方法
handle_migration_fallback() {
    echo "使用备用方法处理迁移问题..."

    # 获取当前数据库版本
    CURRENT_VERSION=$(FLASK_APP=app.py flask db current 2>/dev/null || echo "")
    echo "当前数据库版本: ${CURRENT_VERSION:-'未知'}"

    # 获取所有迁移历史，找到下一个版本
    NEXT_REVISION=$(FLASK_APP=app.py flask db show 2>/dev/null | grep -oP "[a-f0-9]{12}" | head -1)

    if [ ! -z "$NEXT_REVISION" ]; then
        echo "尝试标记下一个迁移为已完成: $NEXT_REVISION"
        FLASK_APP=app.py flask db stamp $NEXT_REVISION 2>/dev/null || true

        # 尝试继续迁移
        FLASK_APP=app.py flask db upgrade 2>/dev/null || true
    fi

    echo "备用处理完成"
}

# 执行迁移
migrate_database

############ 6. 启动后端 API ###########
echo "启动后端 API..."
gunicorn app:app \
    --bind 0.0.0.0:5001 \
    --workers ${GUNICORN_WORKERS:-1} \
    --worker-class gevent \
    --timeout ${GUNICORN_TIMEOUT:-120} &

############ 7. 启动 Next 前端 ##########
echo "启动前端服务..."
cd /app/web
PORT=7860 NODE_ENV=production node server.js &

############ 8. 等待子进程 ###########
echo "服务启动完成，等待进程..."
wait -n