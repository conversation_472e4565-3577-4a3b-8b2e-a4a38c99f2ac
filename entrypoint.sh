#!/usr/bin/env bash
set -e

echo "当前用户: $(whoami), UID: $(id -u), GID: $(id -g)"

############ 1. 环境变量检查 ############
echo "检查必要环境变量..."

# 数据库配置检查
if [ -z "$DB_HOST" ] || [ -z "$DB_DATABASE" ] || [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ]; then
    echo "错误: 缺少数据库配置环境变量"
    echo "必需变量: DB_HOST, DB_DATABASE, DB_USERNAME, DB_PASSWORD"
    exit 1
fi

# Redis 配置检查
if [ -z "$REDIS_HOST" ]; then
    echo "错误: 缺少 REDIS_HOST 环境变量"
    exit 1
fi

############ 2. 数据库配置 ##########
export DATABASE_TYPE=postgresql
export DB_USERNAME=${DB_USERNAME:-}
export DB_PASSWORD=${DB_PASSWORD:-}
export DB_HOST=${DB_HOST:-}
export DB_PORT=${DB_PORT:-26257}
export DB_DATABASE=${DB_DATABASE:-}

# 设置数据库连接池配置
export SQLALCHEMY_POOL_SIZE=${SQLALCHEMY_POOL_SIZE:-30}
export SQLALCHEMY_POOL_RECYCLE=${SQLALCHEMY_POOL_RECYCLE:-3600}
export SQLALCHEMY_ECHO=${SQLALCHEMY_ECHO:-false}

export SQLALCHEMY_DATABASE_URI="postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}"
echo "SQLALCHEMY_DATABASE_URI 已设置"

############ 3. Redis 配置 ##########
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_DB=${REDIS_DB:-0}
export REDIS_USERNAME=${REDIS_USERNAME:-}
export REDIS_PASSWORD=${REDIS_PASSWORD:-}
export REDIS_USE_SSL=${REDIS_USE_SSL:-false}

# 支持 Redis Sentinel 配置
export REDIS_USE_SENTINEL=${REDIS_USE_SENTINEL:-false}
export REDIS_SENTINELS=${REDIS_SENTINELS:-}
export REDIS_SENTINEL_SERVICE_NAME=${REDIS_SENTINEL_SERVICE_NAME:-}
export REDIS_SENTINEL_USERNAME=${REDIS_SENTINEL_USERNAME:-}
export REDIS_SENTINEL_PASSWORD=${REDIS_SENTINEL_PASSWORD:-}
export REDIS_SENTINEL_SOCKET_TIMEOUT=${REDIS_SENTINEL_SOCKET_TIMEOUT:-0.1}

echo "Redis 连接: ${REDIS_HOST}:${REDIS_PORT} (DB: ${REDIS_DB})"

export OPENDAL_SCHEME=fs
export OPENDAL_FS_ROOT=/tmp/storage

############ 4. 连接测试 ##########
echo "测试数据库连接..."
cd /app/api

# 测试数据库连接
python3 -c "
import psycopg2
import sys
import os

try:
    conn_params = {
        'host': '${DB_HOST}',
        'port': ${DB_PORT},
        'database': '${DB_DATABASE}',
        'user': '${DB_USERNAME}',
        'password': '${DB_PASSWORD}'
    }

    if os.getenv('DB_SSL_CA_CERT'):
        conn_params['sslrootcert'] = os.getenv('DB_SSL_CA_CERT')
    if os.getenv('DB_SSL_CLIENT_CERT'):
        conn_params['sslcert'] = os.getenv('DB_SSL_CLIENT_CERT')
    if os.getenv('DB_SSL_CLIENT_KEY'):
        conn_params['sslkey'] = os.getenv('DB_SSL_CLIENT_KEY')

    conn = psycopg2.connect(**conn_params)

    with conn.cursor() as cur:
        cur.execute('SELECT version()')
        version = cur.fetchone()[0]
        print(f'数据库连接成功: {version.split()[0]} {version.split()[1]}')

    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
    sys.exit(1)
"

# 测试 Redis 连接
python3 -c "
import redis
import sys
try:
    redis_kwargs = {
        'host': '${REDIS_HOST}',
        'port': ${REDIS_PORT},
        'db': ${REDIS_DB},
        'ssl': ${REDIS_USE_SSL}
    }

    if '${REDIS_USERNAME}':
        redis_kwargs['username'] = '${REDIS_USERNAME}'
    if '${REDIS_PASSWORD}':
        redis_kwargs['password'] = '${REDIS_PASSWORD}'

    r = redis.Redis(**redis_kwargs)
    r.ping()
    print('Redis 连接成功')
except Exception as e:
    print(f'Redis 连接失败: {e}')
    sys.exit(1)
"

############ 5. 数据库迁移 #############
echo "执行数据库迁移..."
FLASK_APP=app.py flask db upgrade || echo "迁移完成或跳过"

############ 6. 动态生成 Nginx 配置 ############
echo "生成 Nginx 配置..."
cat > /tmp/nginx.conf << 'EOF'
# nginx.conf - 运行时生成的配置

daemon off;
pid /tmp/nginx.pid;
error_log /tmp/nginx_logs/error.log warn;
worker_processes 1;
worker_rlimit_nofile 1024;

events {
    worker_connections 512;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /tmp/nginx_logs/access.log main;

    client_body_temp_path /tmp/nginx_client_temp 1 2;
    proxy_temp_path /tmp/nginx_proxy_temp 1 2;
    fastcgi_temp_path /tmp/nginx_fastcgi_temp 1 2;
    uwsgi_temp_path /tmp/nginx_uwsgi_temp 1 2;
    scgi_temp_path /tmp/nginx_scgi_temp 1 2;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;

    client_max_body_size 100M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    client_body_timeout 12;
    client_header_timeout 12;

    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    upstream frontend {
        server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream backend {
        server 127.0.0.1:5001 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    server {
        listen 7860;
        server_name localhost;

        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        location /nginx-health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location /debug-backend {
            proxy_pass http://backend/health;
            proxy_set_header Host $host;
            add_header X-Debug-Backend "true";
        }

        # 控制台 API 路由 - 最高优先级，精确匹配
        location /console/api {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        # API 路由 - 后端服务
        location /api {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        # V1 API 路由
        location /v1 {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 文件服务路由
        location /files {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 文件上传需要更长的超时时间
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
        }

        # MCP 服务路由
        location /mcp {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 后端特殊路由 - 精确匹配
        location /setup {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 探索页面路由 - 前端服务
        location /explore {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 插件守护进程路由 - 暂时指向后端（如需要可单独部署插件服务）
        location /e/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
        }

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            expires 1d;
            add_header Cache-Control "public, immutable";
        }

        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;

            sub_filter 'http://localhost:5001' '';
            sub_filter 'localhost:5001' '';
            sub_filter_once off;
            sub_filter_types application/javascript text/javascript application/json text/html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            return 500 "Internal Server Error\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF
############ 7. 准备 Nginx 临时目录 ############
echo "准备 Nginx 临时目录..."
mkdir -p /tmp/nginx_client_temp \
         /tmp/nginx_proxy_temp \
         /tmp/nginx_fastcgi_temp \
         /tmp/nginx_uwsgi_temp \
         /tmp/nginx_scgi_temp \
         /tmp/nginx_logs

# 确保目录权限正确
chmod 755 /tmp/nginx_*

############ 7. 启动后端 ############
echo "启动后端 API..."
cd /app/api

# 检查必要的 Python 模块
python3 -c "import flask, gunicorn, gevent" || {
    echo "缺少必要的 Python 模块"
    exit 1
}

# 使用更保守的 gunicorn 配置
gunicorn app:app \
    --bind 127.0.0.1:5001 \
    --workers 1 \
    --worker-class gevent \
    --worker-connections 500 \
    --timeout 120 \
    --max-requests 1000 \
    --max-requests-jitter 50 \
    --log-level info \
    --access-logfile - \
    --error-logfile - \
    --capture-output &

BACKEND_PID=$!

# 等待后端启动
echo "等待后端启动..."
for i in {1..30}; do
    if curl -f -s http://127.0.0.1:5001/health > /dev/null 2>&1; then
        echo "✓ 后端启动成功 (PID: $BACKEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "✗ 后端启动超时"
        exit 1
    fi
    sleep 2
done

############ 8. 启动前端 ############
echo "启动前端服务..."
cd /app/web

# 设置前端环境变量，让前端通过相对路径访问 API
export NEXT_PUBLIC_API_PREFIX=""
export NEXT_PUBLIC_PUBLIC_API_PREFIX=""
export NEXT_PUBLIC_SENTRY_DSN=""
export NEXT_PUBLIC_SITE_ABOUT=""
export NEXT_PUBLIC_EDITION="SELF_HOSTED"

# 尝试在运行时覆盖 API 配置（如果前端支持）
if [ -f ".env.local" ]; then
    rm .env.local
fi

cat > .env.local << EOF
NEXT_PUBLIC_API_PREFIX=
NEXT_PUBLIC_PUBLIC_API_PREFIX=
EOF

# 检查是否有配置文件可以修改
echo "检查前端配置文件..."
find . -name "*.js" -o -name "*.json" | head -5
ls -la .env* 2>/dev/null || echo "没有找到 .env 文件"

# 前端绑定到内部端口
HOSTNAME=127.0.0.1 PORT=3000 NODE_ENV=production node server.js &
FRONTEND_PID=$!

# 等待前端启动
echo "等待前端启动..."
for i in {1..30}; do
    if curl -f -s http://127.0.0.1:3000 > /dev/null 2>&1; then
        echo "✓ 前端启动成功 (PID: $FRONTEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "✗ 前端启动超时"
        exit 1
    fi
    sleep 2
done

############ 10. 测试 Nginx 配置 ############
echo "测试 Nginx 配置..."
if nginx -t -c /tmp/nginx.conf; then
    echo "✓ Nginx 配置测试通过"

    # 检查 sub_filter 模块是否可用
    nginx -V 2>&1 | grep -o "with-http_sub_module" && echo "✓ sub_filter 模块可用" || echo "⚠ sub_filter 模块不可用"
else
    echo "✗ Nginx 配置测试失败"
    echo "显示配置内容："
    cat /tmp/nginx.conf
    exit 1
fi

############ 11. 启动 Nginx ############
echo "启动 Nginx..."
nginx -c /tmp/nginx.conf &
NGINX_PID=$!

# 等待 Nginx 启动
echo "等待 Nginx 启动..."
for i in {1..15}; do
    if curl -f -s http://localhost:7860 > /dev/null 2>&1; then
        echo "✓ Nginx 启动成功 (PID: $NGINX_PID)"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "✗ Nginx 启动超时"
        # 查看错误日志
        echo "Nginx 错误日志："
        cat /tmp/nginx_logs/error.log 2>/dev/null || echo "无法读取错误日志"
        exit 1
    fi
    sleep 2
done

############ 11. 健康检查和调试 ############
echo "执行最终健康检查..."

# 检查各个服务
echo "=== 服务状态检查 ==="
curl -f -s http://127.0.0.1:5001/health && echo "✓ 后端服务正常" || echo "✗ 后端服务异常"
curl -f -s http://127.0.0.1:3000 && echo "✓ 前端服务正常" || echo "✗ 前端服务异常"
curl -f -s http://localhost:7860/nginx-health && echo "✓ Nginx 服务正常" || echo "✗ Nginx 服务异常"

# 测试 API 代理
echo "=== API 代理测试 ==="
curl -f -s http://localhost:7860/api/health && echo "✓ API 代理正常" || echo "✗ API 代理异常"
curl -f -s http://localhost:7860/console/api/health && echo "✓ 控制台 API 代理正常" || echo "✗ 控制台 API 代理异常"
curl -f -s http://localhost:7860/v1/health && echo "✓ V1 API 代理正常" || echo "✗ V1 API 代理异常"
curl -f -s http://localhost:7860/files && echo "✓ 文件服务代理正常" || echo "✗ 文件服务代理异常"
curl -f -s http://localhost:7860/mcp && echo "✓ MCP 服务代理正常" || echo "✗ MCP 服务代理异常"
curl -f -s http://localhost:7860/setup && echo "✓ setup 路由正常" || echo "✗ setup 路由异常"
curl -f -s http://localhost:7860/explore && echo "✓ 探索页面路由正常" || echo "✗ 探索页面路由异常"
curl -f -s http://localhost:7860/e/ && echo "✓ 插件路由正常" || echo "✗ 插件路由异常"

# 检查 Nginx 进程和配置
echo "=== Nginx 状态 ==="
ps aux | grep nginx | grep -v grep
echo "Nginx 配置文件内容："
cat /etc/nginx/nginx.conf | grep -A 10 -B 5 "location"

echo "=========================================="
echo "🎉 所有服务启动完成！"
echo "访问地址: https://edaoren-dify-run.hf.space"
echo "前端: http://localhost:7860"
echo "后端: http://localhost:7860/api"
echo "进程 PIDs: Nginx=$NGINX_PID, 前端=$FRONTEND_PID, 后端=$BACKEND_PID"
echo "=========================================="

# 创建监控函数
monitor_services() {
    while true; do
        sleep 30
        # 检查关键进程是否还在运行
        if ! kill -0 $NGINX_PID 2>/dev/null; then
            echo "❌ Nginx 进程已停止"
            exit 1
        fi
        if ! kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "❌ 前端进程已停止"
            exit 1
        fi
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            echo "❌ 后端进程已停止"
            exit 1
        fi
    done
}

# 优雅关闭处理
cleanup() {
    echo "正在关闭服务..."
    kill $NGINX_PID $FRONTEND_PID $BACKEND_PID 2>/dev/null || true
    wait
    echo "服务已关闭"
}

trap cleanup SIGTERM SIGINT

# 启动监控并等待
monitor_services &
wait