########################
# HF Spaces 用户模式 Nginx + Dify (优化版)
########################
FROM python:3.11-slim

ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# ---- 1. 系统依赖 + Nginx + Node.js ----
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git \
        curl \
        wget \
        ca-certificates \
        build-essential \
        libxml2-dev \
        libxslt1-dev \
        zlib1g-dev \
        libmagic1 \
        libpq-dev \
        nginx \
        && \
    # 安装 Node.js 22.x
    curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    # 清理缓存
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# ---- 2. 创建运行时目录并设置权限 ----
RUN mkdir -p /tmp/nginx_client_temp \
             /tmp/nginx_proxy_temp \
             /tmp/nginx_fastcgi_temp \
             /tmp/nginx_uwsgi_temp \
             /tmp/nginx_scgi_temp \
             /tmp/nginx_logs \
             /tmp/storage && \
    chmod -R 755 /tmp/nginx_* /tmp/storage && \
    # 移除默认配置
    rm -rf /etc/nginx/sites-enabled/default /var/log/nginx/* && \
    # 确保 nginx 可执行
    chmod 755 /usr/sbin/nginx

# ---- 3. 安装 Python 包管理器 ----
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir "uv>=0.6"

# ---- 4. 克隆并准备 Dify 后端 ----
ARG DIFY_TAG=1.7.1
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

WORKDIR /opt/dify/api

# 生成并安装 Python 依赖
RUN uv export --format requirements-txt --no-hashes > /tmp/requirements.txt && \
    echo "psycopg2>=2.9.5" >> /tmp/requirements.txt && \
    echo "sqlalchemy-cockroachdb>=2.0.3" >> /tmp/requirements.txt && \
    echo "gevent>=23.0.0" >> /tmp/requirements.txt && \
    echo "gunicorn>=21.0.0" >> /tmp/requirements.txt && \
    pip install --no-cache-dir -r /tmp/requirements.txt && \
    rm /tmp/requirements.txt

# 复制后端代码
RUN cp -r /opt/dify/api /app/api && \
    rm -rf /opt/dify

# ---- 5. 准备前端 ----
COPY frontend.tgz /tmp/frontend.tgz
RUN mkdir -p /app/web && \
    tar -xzf /tmp/frontend.tgz -C /app/web && \
    rm /tmp/frontend.tgz

WORKDIR /app/web

# 设置前端环境变量
ENV NODE_ENV=production \
    HUSKY=0 \
    NEXT_TELEMETRY_DISABLED=1

# 安装前端依赖
RUN npm install -g pnpm@latest && \
    # 清理可能冲突的文件
    rm -rf node_modules package-lock.json && \
    # 删除可能不兼容的 pnpm-lock.yaml，让 pnpm 重新生成
    rm -f pnpm-lock.yaml && \
    # 安装生产依赖
    pnpm install --prod --ignore-scripts && \
    # 清理缓存
    pnpm store prune && \
    rm -rf /root/.pnpm-store /root/.npm /tmp/*

# ---- 6. 复制配置文件 ----
COPY nginx.conf /etc/nginx/nginx.conf
COPY entrypoint.sh /entrypoint.sh

# 设置执行权限
RUN chmod +x /entrypoint.sh

# ---- 7. 创建应用必需目录 ----
RUN mkdir -p /app/certs /app/logs

# ---- 8. 健康检查 ----
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:7860/nginx-health || exit 1

# ---- 9. 暴露端口 ----
EXPOSE 7860

# ---- 10. 设置工作目录和启动命令 ----
WORKDIR /app
CMD ["/entrypoint.sh"]